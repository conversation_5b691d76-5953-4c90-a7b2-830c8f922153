'use client';

import React, { useState, useRef } from 'react';
import { MangaCanvas, MangaCanvasRef } from '@/components/canvas/MangaCanvas';

import { TextRegionEditor } from '@/components/canvas/TextRegionEditor';
import { TextRegionList } from '@/components/canvas/TextRegionList';
import { TextRegionManager } from '@/components/canvas/TextRegionManager';
import { OCRProcessor } from '@/components/ocr/OCRProcessor';
import { TranslationProcessor } from '@/components/translation/TranslationProcessor';
import { CanvasToolbar } from '@/components/canvas/CanvasToolbar';
import { CanvasNavigationControls } from '@/components/canvas/CanvasNavigationControls';
import { CanvasMinimap } from '@/components/canvas/CanvasMinimap';
import { CanvasGridRulers } from '@/components/canvas/CanvasGridRulers';
import { ProjectPages } from '@/components/project/ProjectPages';
import { PageUpload } from '@/components/project/PageUpload';
import { OCRJobHistory } from '@/components/ocr/OCRJobHistory';
import { OCRStatistics } from '@/components/ocr/OCRStatistics';
import { TranslationJobHistory } from '@/components/translation/TranslationJobHistory';
import { TranslationStatistics } from '@/components/translation/TranslationStatistics';
import { ProjectDetail } from '@/components/project/ProjectDetail';
import { PageDetail } from '@/components/project/PageDetail';
import { ModelSelector } from '@/components/llm/ModelSelector';
import { LLMProviderSettings } from '@/components/settings/LLMProviderSettings';
import { LoadingOverlay, Spinner } from '@/components/feedback/LoadingStates';
import { ErrorDisplay } from '@/components/feedback/ErrorDisplay';
import { useCanvas } from '@/hooks/useCanvas';
import { useAppContext } from '@/store/AppContext';
import {
  CanvasState,
  CanvasTextRegion
} from '@/types/canvas';
import { ProjectResponse, ProjectPageResponse, TextRegionType, TranslationStatus } from '@/types/api';

interface TranslationLayoutProps {
  currentProject?: ProjectResponse | null;
  currentPage?: ProjectPageResponse | null;
  imageUrl?: string;
  onProjectChange?: (project: ProjectResponse) => void;
  onPageChange?: (pageId: string) => void;
  className?: string;
}

export const TranslationLayout: React.FC<TranslationLayoutProps> = ({
  currentProject: propCurrentProject,
  currentPage: propCurrentPage,
  imageUrl: propImageUrl,
  onProjectChange,
  onPageChange,
  className = ''
}) => {
  const canvasRef = useRef<MangaCanvasRef>(null);
  const { state, dispatch } = useAppContext();

  // Use AppContext state or fallback to props for backward compatibility
  const currentProject = state.currentProject || propCurrentProject;
  const currentPage = state.currentPage || propCurrentPage;
  const imageUrl = propImageUrl || (currentPage?.file_path ?
    `${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'}${currentPage.file_path}` :
    undefined
  );

  const [selectedRegionId, setSelectedRegionId] = useState<string | undefined>();
  const [activeNavTab, setActiveNavTab] = useState<'regions' | 'pages' | 'upload' | 'statistics' | 'details' | 'settings'>('regions');

  // Canvas hook for managing canvas state and operations
  const {
    imageInfo,
    initializeCanvas,
    updateCanvasState,
    zoomIn,
    zoomOut,
    resetZoom,
    setTool
  } = useCanvas({
    onRegionCreate: handleRegionCreate,
    onRegionUpdate: handleRegionUpdate,
    onRegionDelete: handleRegionDelete,
    onRegionSelect: handleRegionSelect
  });

  // Handle canvas state changes
  const handleCanvasStateChange = (canvasState: CanvasState) => {
    updateCanvasState(canvasState);
    dispatch({ type: 'UPDATE_CANVAS_STATE', payload: canvasState });
  };

  // Handle text region operations
  function handleRegionCreate(region: Partial<CanvasTextRegion>) {
    const newRegion: CanvasTextRegion = {
      id: `region-${Date.now()}`,
      page_id: currentPage?.id || '',
      region_type: region.region_type || TextRegionType.SPEECH_BUBBLE,
      x: region.x || 0,
      y: region.y || 0,
      width: region.width || 0.1,
      height: region.height || 0.1,
      original_text: region.original_text,
      confidence_score: region.confidence_score,
      translated_text: region.translated_text,
      translation_status: TranslationStatus.PENDING,
      font_family: region.font_family,
      font_size: region.font_size,
      font_color: region.font_color,
      background_color: region.background_color,
      isSelected: false,
      isEditing: false,
      borderColor: region.borderColor || '#3b82f6',
      fillOpacity: region.fillOpacity || 0.2,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    dispatch({ type: 'ADD_TEXT_REGION', payload: newRegion });
    setSelectedRegionId(newRegion.id);
  }

  function handleRegionUpdate(regionId: string, updates: Partial<CanvasTextRegion>) {
    dispatch({
      type: 'UPDATE_TEXT_REGION',
      payload: { id: regionId, updates: { ...updates, updated_at: new Date().toISOString() } }
    });
  }

  function handleRegionDelete(regionId: string) {
    dispatch({ type: 'DELETE_TEXT_REGION', payload: regionId });
    if (selectedRegionId === regionId) {
      setSelectedRegionId(undefined);
    }
  }

  function handleRegionSelect(regionId: string) {
    setSelectedRegionId(regionId);
    // Update selection state for all regions
    state.textRegions.forEach(region => {
      dispatch({
        type: 'UPDATE_TEXT_REGION',
        payload: { id: region.id, updates: { isSelected: region.id === regionId } }
      });
    });
  }

  // Page management handlers
  const handlePageSelect = (page: ProjectPageResponse) => {
    if (onPageChange) {
      onPageChange(page.id);
    } else {
      dispatch({ type: 'SET_CURRENT_PAGE', payload: page });
    }
  };

  const handlePageUploaded = (page: ProjectPageResponse) => {
    // Refresh the current project to get updated page list
    if (currentProject && onProjectChange) {
      onProjectChange(currentProject);
    }
    // Select the newly uploaded page
    handlePageSelect(page);
    // Switch to pages tab to show the uploaded page
    setActiveNavTab('pages');
  };

  // Panel toggle functions
  const togglePanel = (panel: 'showProjectPanel' | 'showTextEditPanel' | 'showOCRPanel' | 'showTranslationPanel') => {
    dispatch({
      type: 'SET_PANEL_VISIBILITY',
      payload: { panel, visible: !state.ui[panel] }
    });
  };

  // Get selected region
  const selectedRegion = state.textRegions.find(region => region.id === selectedRegionId);

  return (
    <div className={`flex h-screen bg-gray-50 ${className}`}>
      {/* Left Sidebar - Project Navigation */}
      {state.ui.showProjectPanel && (
        <div className="w-80 bg-white border-r border-gray-200 flex flex-col">
          {/* Header */}
          <div className="p-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h2 className="text-lg font-semibold text-gray-900">Projects</h2>
              <button
                onClick={() => togglePanel('showProjectPanel')}
                className="p-1 text-gray-400 hover:text-gray-600 rounded"
                title="Hide project panel"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          </div>

          {/* Project Info */}
          {currentProject && (
            <div className="p-4 border-b border-gray-200 bg-blue-50">
              <h3 className="font-medium text-gray-900 mb-1">Current Project</h3>
              <p className="text-sm text-gray-600">{currentProject.name}</p>
              <div className="flex items-center mt-2 text-xs text-gray-500">
                <span className="mr-4">{currentProject.source_language} → {currentProject.target_language}</span>
                {currentPage && <span>{currentPage.text_region_count} regions</span>}
              </div>
              {currentPage && (
                <div className="mt-2 text-xs text-gray-500">
                  <span>Page {currentPage.page_number} - {currentPage.original_filename}</span>
                </div>
              )}
            </div>
          )}

          {/* Navigation Tabs */}
          <div className="border-b border-gray-200">
            <nav className="flex">
              <button
                onClick={() => setActiveNavTab('regions')}
                className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${activeNavTab === 'regions'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
              >
                Regions ({state.textRegions.length})
              </button>
              <button
                onClick={() => setActiveNavTab('pages')}
                className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${activeNavTab === 'pages'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
              >
                Pages
              </button>
              <button
                onClick={() => setActiveNavTab('upload')}
                className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${activeNavTab === 'upload'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
              >
                Upload
              </button>
              <button
                onClick={() => setActiveNavTab('statistics')}
                className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${activeNavTab === 'statistics'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
              >
                Stats
              </button>
            </nav>
          </div>

          {/* Tab Content */}
          <div className="flex-1 overflow-hidden relative">
            {/* Text Regions Tab */}
            {activeNavTab === 'regions' && (
              <>
                {/* Loading indicator for text regions */}
                {state.loading.saving && (
                  <div className="absolute top-2 right-2 z-10">
                    <Spinner size="sm" className="text-blue-600" />
                  </div>
                )}

                <TextRegionList
                  textRegions={state.textRegions}
                  selectedRegionId={selectedRegionId}
                  onRegionSelect={handleRegionSelect}
                  onRegionUpdate={handleRegionUpdate}
                  onRegionDelete={handleRegionDelete}
                  className="h-full border-0 rounded-none shadow-none"
                />

                {/* Error display for saving operations */}
                {state.errors.saving && (
                  <div className="absolute bottom-4 left-4 right-4 z-10">
                    <ErrorDisplay
                      message={state.errors.saving}
                      severity="warning"
                      onDismiss={() => {
                        dispatch({ type: 'SET_ERROR', payload: { key: 'saving', error: null } });
                      }}
                      className="text-sm"
                    />
                  </div>
                )}
              </>
            )}

            {/* Project Pages Tab */}
            {activeNavTab === 'pages' && currentProject && (
              <ProjectPages
                projectId={currentProject.id}
                onPageSelect={handlePageSelect}
                selectedPageId={currentPage?.id}
                className="h-full"
              />
            )}

            {/* Upload Tab */}
            {activeNavTab === 'upload' && currentProject && (
              <div className="h-full p-4">
                <PageUpload
                  projectId={currentProject.id}
                  onPageUploaded={handlePageUploaded}
                  onClose={() => setActiveNavTab('pages')}
                  className="h-full"
                />
              </div>
            )}

            {/* Statistics Tab */}
            {activeNavTab === 'statistics' && (
              <div className="h-full overflow-y-auto">
                <div className="p-4 space-y-6">
                  {/* OCR Statistics */}
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">OCR Statistics</h3>
                    <OCRStatistics className="bg-white rounded-lg border border-gray-200" />
                  </div>

                  {/* Translation Statistics */}
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Translation Statistics</h3>
                    <TranslationStatistics className="bg-white rounded-lg border border-gray-200" />
                  </div>

                  {/* Job History for Current Page */}
                  {currentPage && (
                    <>
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-4">OCR Job History</h3>
                        <div className="bg-white rounded-lg border border-gray-200">
                          <OCRJobHistory
                            pageId={currentPage.id}
                            className="max-h-64 overflow-y-auto"
                          />
                        </div>
                      </div>

                      {/* Translation Job History for selected region */}
                      {selectedRegionId && (
                        <div>
                          <h3 className="text-lg font-semibold text-gray-900 mb-4">Translation Job History</h3>
                          <div className="bg-white rounded-lg border border-gray-200">
                            <TranslationJobHistory
                              regionId={selectedRegionId}
                              className="max-h-64 overflow-y-auto"
                            />
                          </div>
                        </div>
                      )}
                    </>
                  )}

                  {/* Empty state when no page selected */}
                  {!currentPage && (
                    <div className="text-center py-8 text-gray-500">
                      <svg className="w-12 h-12 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                      </svg>
                      <p className="text-sm">Select a page to view detailed job history</p>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Empty state for tabs when no project */}
            {!currentProject && (activeNavTab === 'pages' || activeNavTab === 'upload' || activeNavTab === 'statistics') && (
              <div className="flex items-center justify-center h-full text-gray-500">
                <div className="text-center">
                  <svg className="w-12 h-12 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                  </svg>
                  <p className="text-sm">Select a project to {activeNavTab === 'statistics' ? 'view statistics' : 'manage pages'}</p>
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Main Content Area */}
      <div className="flex-1 flex flex-col">
        {/* Top Toolbar */}
        <div className="bg-white border-b border-gray-200 px-4 py-3">
          <div className="flex items-center justify-between">
            {/* Left side - Canvas controls */}
            <div className="flex items-center space-x-4">
              {!state.ui.showProjectPanel && (
                <button
                  onClick={() => togglePanel('showProjectPanel')}
                  className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded"
                  title="Show project panel"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                  </svg>
                </button>
              )}

              <CanvasToolbar
                canvasState={state.canvas}
                onZoomIn={zoomIn}
                onZoomOut={zoomOut}
                onResetZoom={resetZoom}
                onFitToScreen={() => {
                  // Implement fit to screen functionality
                  resetZoom();
                }}
                onToolChange={setTool}
                onUndo={() => {
                  // TODO: Implement undo functionality
                  console.log('Undo');
                }}
                onRedo={() => {
                  // TODO: Implement redo functionality
                  console.log('Redo');
                }}
                onSave={() => {
                  // TODO: Implement save functionality
                  console.log('Save');
                }}
                onExport={() => {
                  // TODO: Implement export functionality
                  console.log('Export');
                }}
                canUndo={false}
                canRedo={false}
                className="border-0 shadow-none bg-transparent p-0"
              />
            </div>

            {/* Right side - Panel toggles */}
            <div className="flex items-center space-x-2">
              <button
                onClick={() => togglePanel('showOCRPanel')}
                className={`px-3 py-1.5 text-sm font-medium rounded-md transition-colors ${state.ui.showOCRPanel
                  ? 'bg-blue-100 text-blue-700'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                  }`}
              >
                OCR
              </button>
              <button
                onClick={() => togglePanel('showTranslationPanel')}
                className={`px-3 py-1.5 text-sm font-medium rounded-md transition-colors ${state.ui.showTranslationPanel
                  ? 'bg-blue-100 text-blue-700'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                  }`}
              >
                Translation
              </button>
              <button
                onClick={() => togglePanel('showTextEditPanel')}
                className={`px-3 py-1.5 text-sm font-medium rounded-md transition-colors ${state.ui.showTextEditPanel
                  ? 'bg-blue-100 text-blue-700'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                  }`}
              >
                Edit
              </button>
            </div>
          </div>
        </div>

        {/* Canvas Area */}
        <div className="flex-1 flex">
          {/* Canvas Container */}
          <div className="flex-1 p-4 flex items-center justify-center relative">
            <div className="relative">
              {/* Canvas Grid and Rulers */}
              <CanvasGridRulers
                canvasState={state.canvas}
                imageInfo={imageInfo}
                showGrid={state.ui.showGrid}
                showRulers={state.ui.showRulers}
                className="absolute inset-0 pointer-events-none"
              />

              <MangaCanvas
                ref={canvasRef}
                imageUrl={imageUrl}
                width={800}
                height={600}
                onCanvasReady={initializeCanvas}
                onStateChange={handleCanvasStateChange}
                eventHandlers={{
                  onRegionCreate: handleRegionCreate,
                  onRegionUpdate: handleRegionUpdate,
                  onRegionDelete: handleRegionDelete,
                  onRegionSelect: handleRegionSelect
                }}
                className="shadow-lg"
              />

              {/* Text Region Manager */}
              <TextRegionManager
                canvas={canvasRef.current?.getCanvas() || null}
                imageInfo={imageInfo}
                textRegions={state.textRegions}
                onRegionCreate={handleRegionCreate}
                onRegionUpdate={handleRegionUpdate}
                onRegionDelete={handleRegionDelete}
                onRegionSelect={handleRegionSelect}
                selectedRegionId={selectedRegionId}
              />
            </div>

            {/* Canvas Navigation Controls - Floating */}
            <div className="absolute bottom-4 left-4">
              <CanvasNavigationControls
                canvasState={state.canvas}
                onZoomIn={zoomIn}
                onZoomOut={zoomOut}
                onResetZoom={resetZoom}
                onFitToScreen={() => resetZoom()}
                onPan={(deltaX, deltaY) => {
                  // TODO: Implement pan functionality
                  console.log('Pan:', deltaX, deltaY);
                }}
                onToolChange={setTool}
                onToggleGrid={() => {
                  dispatch({
                    type: 'SET_PANEL_VISIBILITY',
                    payload: { panel: 'showGrid' as any, visible: !state.ui.showGrid }
                  });
                }}
                onToggleRulers={() => {
                  dispatch({
                    type: 'SET_PANEL_VISIBILITY',
                    payload: { panel: 'showRulers' as any, visible: !state.ui.showRulers }
                  });
                }}
                showGrid={state.ui.showGrid}
                showRulers={state.ui.showRulers}
                className="bg-white rounded-lg shadow-lg"
              />
            </div>

            {/* Canvas Minimap - Floating */}
            <div className="absolute bottom-4 right-4">
              <CanvasMinimap
                canvasState={state.canvas}
                imageInfo={imageInfo}
                textRegions={state.textRegions}
                onViewportChange={(x, y) => {
                  // TODO: Implement viewport change
                  console.log('Viewport change:', x, y);
                }}
                width={200}
                height={150}
                className="bg-white rounded-lg shadow-lg border border-gray-200"
              />
            </div>
          </div>

          {/* Right Sidebar - Text Editor */}
          {state.ui.showTextEditPanel && (
            <div className="w-80 bg-white border-l border-gray-200">
              <TextRegionEditor
                region={selectedRegion || null}
                onUpdate={handleRegionUpdate}
                onDelete={handleRegionDelete}
                onClose={() => setSelectedRegionId(undefined)}
                className="h-full border-0 rounded-none shadow-none"
              />
            </div>
          )}
        </div>
      </div>

      {/* OCR Panel (Modal/Overlay) */}
      {state.ui.showOCRPanel && currentPage && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden relative">
            {/* OCR Loading Overlay */}
            <LoadingOverlay
              isVisible={state.loading.ocr}
              message="Processing OCR..."
              className="z-10"
            />

            <OCRProcessor
              pageId={currentPage.id}
              onOCRComplete={(regions) => {
                // Convert OCR results to canvas text regions and add them
                dispatch({ type: 'SET_TEXT_REGIONS', payload: regions });
                togglePanel('showOCRPanel');
              }}
              onClose={() => togglePanel('showOCRPanel')}
              className="h-full"
            />

            {/* OCR Error Display */}
            {state.errors.ocr && (
              <div className="absolute top-4 right-4 z-20 max-w-md">
                <ErrorDisplay
                  title="OCR Error"
                  message={state.errors.ocr}
                  severity="error"
                  onDismiss={() => {
                    dispatch({ type: 'SET_ERROR', payload: { key: 'ocr', error: null } });
                  }}
                />
              </div>
            )}
          </div>
        </div>
      )}

      {/* Translation Panel (Modal/Overlay) */}
      {state.ui.showTranslationPanel && currentProject && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden relative">
            {/* Translation Loading Overlay */}
            <LoadingOverlay
              isVisible={state.loading.translation}
              message="Processing translation..."
              className="z-10"
            />

            <TranslationProcessor
              textRegions={state.textRegions}
              selectedRegionIds={selectedRegionId ? [selectedRegionId] : []}
              sourceLanguage={currentProject.source_language}
              targetLanguage={currentProject.target_language}
              onTranslationComplete={(regionId, translatedText) => {
                // Update the specific text region with the translation
                dispatch({
                  type: 'UPDATE_TEXT_REGION',
                  payload: { id: regionId, updates: { translated_text: translatedText } }
                });
              }}
              onClose={() => togglePanel('showTranslationPanel')}
              className="h-full"
            />

            {/* Translation Error Display */}
            {state.errors.translation && (
              <div className="absolute top-4 right-4 z-20 max-w-md">
                <ErrorDisplay
                  title="Translation Error"
                  message={state.errors.translation}
                  severity="error"
                  onDismiss={() => {
                    dispatch({ type: 'SET_ERROR', payload: { key: 'translation', error: null } });
                  }}
                />
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default TranslationLayout;
