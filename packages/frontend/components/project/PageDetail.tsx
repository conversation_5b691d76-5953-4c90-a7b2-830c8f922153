'use client';

import React, { useState, useEffect } from 'react';
import { ProjectPageDetailResponse, TextRegionResponse } from '@/types/api';
import { projectsAPI } from '@/lib/api-client';
import { buildImageUrl } from '@/lib/api-utils';
import { LoadingOverlay, Spinner } from '@/components/feedback/LoadingStates';
import { ErrorDisplay } from '@/components/feedback/ErrorDisplay';

interface PageDetailProps {
  projectId: string;
  pageId: string;
  onTextRegionSelect?: (region: TextRegionResponse) => void;
  onClose?: () => void;
  className?: string;
}

export const PageDetail: React.FC<PageDetailProps> = ({
  projectId,
  pageId,
  onTextRegionSelect,
  onClose,
  className = ''
}) => {
  const [pageDetail, setPageDetail] = useState<ProjectPageDetailResponse | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedRegionId, setSelectedRegionId] = useState<string | null>(null);

  // Load page detail
  useEffect(() => {
    const loadPageDetail = async () => {
      try {
        setIsLoading(true);
        setError(null);
        const detail = await projectsAPI.getProjectPageDetail(projectId, pageId);
        setPageDetail(detail);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load page details');
      } finally {
        setIsLoading(false);
      }
    };

    if (projectId && pageId) {
      loadPageDetail();
    }
  }, [projectId, pageId]);

  const handleRegionSelect = (region: TextRegionResponse) => {
    setSelectedRegionId(region.id);
    onTextRegionSelect?.(region);
  };

  if (isLoading) {
    return (
      <div className={`bg-white rounded-lg shadow-lg ${className}`}>
        <LoadingOverlay isVisible={true} message="Loading page details..." />
      </div>
    );
  }

  if (error) {
    return (
      <div className={`bg-white rounded-lg shadow-lg p-6 ${className}`}>
        <ErrorDisplay
          title="Failed to load page details"
          message={error}
          severity="error"
          onRetry={() => window.location.reload()}
          onDismiss={onClose}
        />
      </div>
    );
  }

  if (!pageDetail) {
    return null;
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getRegionTypeColor = (type: string) => {
    switch (type) {
      case 'speech_bubble': return 'bg-blue-100 text-blue-800';
      case 'thought_bubble': return 'bg-purple-100 text-purple-800';
      case 'narration': return 'bg-green-100 text-green-800';
      case 'sound_effect': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getTranslationStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800';
      case 'in_progress': return 'bg-yellow-100 text-yellow-800';
      case 'failed': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className={`bg-white rounded-lg shadow-lg ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between p-6 border-b border-gray-200">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">
            Page {pageDetail.page_number}
          </h2>
          <p className="text-gray-600 mt-1">{pageDetail.original_filename}</p>
        </div>
        {onClose && (
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
            title="Close"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        )}
      </div>

      <div className="flex h-96">
        {/* Image Preview */}
        <div className="flex-1 p-6">
          <div className="h-full bg-gray-100 rounded-lg overflow-hidden">
            <img
              src={buildImageUrl(pageDetail.file_path)}
              alt={`Page ${pageDetail.page_number}`}
              className="w-full h-full object-contain"
              onError={(e) => {
                const target = e.target as HTMLImageElement;
                target.src = '/placeholder-image.svg';
              }}
            />
          </div>
        </div>

        {/* Page Info */}
        <div className="w-80 border-l border-gray-200 p-6">
          <div className="space-y-4">
            {/* Stats */}
            <div className="grid grid-cols-2 gap-4">
              <div className="bg-blue-50 rounded-lg p-3">
                <div className="text-lg font-bold text-blue-600">{pageDetail.text_regions.length}</div>
                <div className="text-sm text-blue-800">Text Regions</div>
              </div>
              
              <div className="bg-green-50 rounded-lg p-3">
                <div className="text-lg font-bold text-green-600">
                  {pageDetail.text_regions.filter(r => r.translation_status === 'completed').length}
                </div>
                <div className="text-sm text-green-800">Translated</div>
              </div>
            </div>

            {/* File Info */}
            <div className="bg-gray-50 rounded-lg p-3">
              <div className="text-sm space-y-1">
                <div><strong>File:</strong> {pageDetail.original_filename}</div>
                <div><strong>Size:</strong> {pageDetail.width} × {pageDetail.height}</div>
                <div><strong>Created:</strong> {formatDate(pageDetail.created_at)}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Text Regions */}
      <div className="p-6 border-t border-gray-200">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          Text Regions ({pageDetail.text_regions.length})
        </h3>
        
        {pageDetail.text_regions.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <svg className="w-12 h-12 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z" />
            </svg>
            <p className="text-sm">No text regions detected</p>
            <p className="text-xs text-gray-400 mt-1">Run OCR to detect text regions</p>
          </div>
        ) : (
          <div className="space-y-3 max-h-64 overflow-y-auto">
            {pageDetail.text_regions.map((region) => (
              <div
                key={region.id}
                onClick={() => handleRegionSelect(region)}
                className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                  selectedRegionId === region.id
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                }`}
              >
                <div className="flex items-start justify-between mb-2">
                  <div className="flex items-center space-x-2">
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getRegionTypeColor(region.region_type)}`}>
                      {region.region_type.replace('_', ' ')}
                    </span>
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getTranslationStatusColor(region.translation_status)}`}>
                      {region.translation_status}
                    </span>
                  </div>
                  {region.confidence_score && (
                    <span className="text-xs text-gray-500">
                      {Math.round(region.confidence_score * 100)}% confidence
                    </span>
                  )}
                </div>
                
                {region.original_text && (
                  <div className="mb-2">
                    <div className="text-sm font-medium text-gray-700">Original:</div>
                    <div className="text-sm text-gray-900 bg-gray-100 rounded p-2 mt-1">
                      {region.original_text}
                    </div>
                  </div>
                )}
                
                {region.translated_text && (
                  <div>
                    <div className="text-sm font-medium text-gray-700">Translation:</div>
                    <div className="text-sm text-gray-900 bg-blue-100 rounded p-2 mt-1">
                      {region.translated_text}
                    </div>
                  </div>
                )}
                
                {!region.original_text && !region.translated_text && (
                  <div className="text-sm text-gray-500 italic">
                    No text content
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};
